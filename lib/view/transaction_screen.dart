import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:zero_koin/controllers/theme_controller.dart';
import 'package:zero_koin/controllers/admob_controller.dart';
import 'package:zero_koin/widgets/app_bar_container.dart';
import 'package:zero_koin/widgets/my_drawer.dart';

class TransactionsScreen extends StatefulWidget {
  const TransactionsScreen({super.key});
  @override
  State<TransactionsScreen> createState() => _TransactionsScreenState();
}

class _TransactionsScreenState extends State<TransactionsScreen> {
  final AdMobController _adMobController = Get.find<AdMobController>();

  @override
  void initState() {
    super.initState();
    // Load interstitial ad when screen initializes
    _adMobController.loadInterstitialAd();

    // Wait for the screen to build, then wait for ad to be ready
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _waitAndShowAd();
    });
  }

  void _waitAndShowAd() {
    // Check if ad is ready, if not wait and check again
    if (_adMobController.isInterstitialAdReady.value) {
      _adMobController.showInterstitialAd();
    } else {
      // Wait 500ms and check again, repeat until ad is ready or timeout
      Future.delayed(const Duration(milliseconds: 500), () {
        if (mounted) {
          _waitAndShowAd();
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final double screenHeight = MediaQuery.sizeOf(context).height;
    final ThemeController themeController = Get.find<ThemeController>();
    return Scaffold(
      drawer: MyDrawer(),
      backgroundColor: themeController.backgroundColor,
      body: Stack(
        children: [
          Image.asset(
            'assets/Background.jpg',
            fit: BoxFit.cover,
            height: double.infinity,
            width: double.infinity,
          ),
          Container(
            color: Colors.black.withOpacity(0.35),
            height: double.infinity,
            width: double.infinity,
          ),
          Column(
            children: [
              AppBarContainer(
                color: Colors.black.withOpacity(0.6),
                showTotalPosition: false,
              ),
              SizedBox(height: 20),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Row(
                  children: [
                    GestureDetector(
                      onTap: () => Navigator.pop(context),
                      child: const Icon(
                        Icons.arrow_back,
                        color: Colors.white,
                        size: 28,
                      ),
                    ),
                    const SizedBox(width: 12),
                    const Text(
                      "Transactions",
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 32),
              Expanded(
                child: Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: themeController.contentBackgroundColor,
                  ),
                  child: Column(
                    children: [
                      const SizedBox(height: 48),
                      SizedBox(
                        height: 120,
                        child: Image.asset(
                          "assets/oops.png",
                          fit: BoxFit.contain,
                        ),
                      ),
                      const SizedBox(height: 12),
                      Text(
                        "Oops!",
                        style: TextStyle(
                          fontSize: 38,
                          color: themeController.subtitleColor,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        "Nothing great ever come that easy.",
                        style: TextStyle(
                          fontSize: 18,
                          color: themeController.subtitleColor,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
